"""
ArLocal Python Implementation
A Flask-based reimplementation of the ArLocal Arweave local testnet
Organized using Flask blueprints for better maintainability
"""

from flask import Flask, jsonify
from flask_cors import CORS
import logging
import os
from config import config
from database import ArLocalDatabase

# Import blueprints
from blueprints import base_bp, transactions_bp, wallet_bp, graphql_bp

# Get configuration
config_name = os.environ.get('ARLOCAL_CONFIG', 'default')
app_config = config[config_name]

# Configure logging
logging.basicConfig(level=getattr(logging, app_config.LOG_LEVEL))
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(app_config)
CORS(app, origins=app_config.CORS_ORIGINS)

# Initialize database
db = ArLocalDatabase(
    db_path=app_config.DATABASE_PATH,
    data_dir=app_config.DATA_DIR
)

# Network configuration
network_config = {
    "network": app_config.NETWORK_NAME,
    "version": app_config.NETWORK_VERSION,
    "release": app_config.NETWORK_RELEASE,
    "peers": 1,
    "node_state_latency": 0
}

# In-memory logs (shared across blueprints)
logs = []

# Make database, network config, and logs available to blueprints
app.db = db
app.network_config = network_config
app.logs = logs

# Register blueprints
app.register_blueprint(base_bp)
app.register_blueprint(transactions_bp)
app.register_blueprint(wallet_bp)
app.register_blueprint(graphql_bp)

# Catch-all route for unmatched requests
@app.route('/<path:other>', methods=['GET'])
def catch_all(other: str):
    """Catch-all route for unmatched requests"""
    return jsonify({
        "status": 400,
        "error": "Request type not found."
    }), 400

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"status": 404, "error": "Not Found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"status": 500, "error": "Internal Server Error"}), 500

if __name__ == '__main__':
    print(f"Starting ArLocal Python server on {app_config.HOST}:{app_config.PORT}...")
    print(f"Network: {app_config.NETWORK_NAME}")
    print(f"Debug mode: {app_config.DEBUG}")
    print("Available endpoints:")
    print("  GET  /                   - Network status")
    print("  GET  /info               - Network info")
    print("  GET  /peers              - Peer list")
    print("  GET  /logs               - Application logs")
    print("  GET  /reset              - Reset network")
    print("  GET  /tx_anchor          - Transaction anchor")
    print("  GET  /price/<bytes>      - Calculate price")
    print("  GET  /tx/pending         - Pending transactions")
    print("  GET  /tx/<txid>          - Get transaction")
    print("  POST /tx                 - Submit transaction")
    print("  GET  /mine/<qty>         - Mine blocks")
    print("  POST /wallet             - Create wallet")
    print("  GET  /wallet/<addr>/balance - Get balance")
    print("  GET  /mint/<addr>/<amt>  - Mint tokens")
    print("  POST /chunk              - Submit chunk")
    print("  GET  /chunk/<offset>     - Get chunk")
    print("  POST /graphql            - GraphQL endpoint (full Arweave API)")
    print("  GET  /graphql            - GraphQL schema info")
    print()

    app.run(host=app_config.HOST, port=app_config.PORT, debug=app_config.DEBUG)
