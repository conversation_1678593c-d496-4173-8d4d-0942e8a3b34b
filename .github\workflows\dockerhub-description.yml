name: Update Docker Hub Description
on:
  push:
    branches:
      - main
    paths:
      - docker.md
      - .github/workflows/dockerhub-description.yml
jobs:
  dockerHubDescription:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Docker Hub Description
      uses: peter-evans/dockerhub-description@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: textury/arlocal
        short-description: ${{ github.event.repository.description }}