# Arlocal

Run a local Arweave gateway-like server.

## Getting Started
### Prerequisites


In order to run this container you'll need docker installed.

* [Windows](https://docs.docker.com/windows/started)
* [OS X](https://docs.docker.com/mac/started/)
* [Linux](https://docs.docker.com/linux/started/)

### Usage

```shell
docker run --name arlocal -p 1984:1984 textury/arlocal
```

## Built With

* Nodejs v16

## Find Us

* [GitHub](https://github.com/textury)
* [Textury.org](https://textury.org)

## Contributing

Please read [CONTRIBUTING](https://github.com/textury/arlocal#contributing) for details on our code of conduct, and the process for submitting pull requests to us.

## Versioning

For the versions available, see the 
[tags on this repository](https://github.com/textury/arlocal/tags). 

## Authors

See also the list of [contributors](https://github.com/textury/arlocal/contributors) who 
participated in this project.

## License

This project is licensed under the MIT License - see the [LICENSE](https://github.com/textury/arlocal/blob/main/LICENSE) file for details.
