#!/usr/bin/env python3
"""
Test script to verify transaction ID calculation
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'arlocal_python'))

from utils import calculate_transaction_id, sha256_b64url, b64url_to_bytes

def test_transaction_id_calculation():
    """Test transaction ID calculation with known values"""
    
    # Test with a sample signature (base64url encoded)
    test_signature = "dGVzdF9zaWduYXR1cmVfZGF0YQ"  # "test_signature_data" in base64url
    
    print(f"Test signature: {test_signature}")
    
    # Calculate transaction ID
    txid = calculate_transaction_id(test_signature)
    print(f"Calculated transaction ID: {txid}")
    
    # Verify the calculation manually
    signature_bytes = b64url_to_bytes(test_signature)
    print(f"Signature bytes: {signature_bytes}")
    
    manual_hash = sha256_b64url(signature_bytes)
    print(f"Manual hash calculation: {manual_hash}")
    
    # They should match
    assert txid == manual_hash, f"Transaction ID mismatch: {txid} != {manual_hash}"
    print("✓ Transaction ID calculation is correct!")
    
    # Test with empty signature (should fallback to random)
    empty_txid = calculate_transaction_id("")
    print(f"Empty signature fallback ID: {empty_txid}")
    assert len(empty_txid) == 43, "Fallback ID should be 43 characters"
    print("✓ Empty signature fallback works!")

if __name__ == "__main__":
    test_transaction_id_calculation()
    print("\nAll tests passed!")
