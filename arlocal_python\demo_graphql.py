"""
GraphQL Demo and Test Script
This script demonstrates the complete GraphQL functionality
"""

import urllib.request
import json
import time

BASE_URL = "http://localhost:1984"

def make_request(method, url, data=None):
    """Make HTTP request"""
    headers = {}
    if data:
        data = json.dumps(data).encode('utf-8')
        headers['Content-Type'] = 'application/json'
    
    req = urllib.request.Request(url, data=data, headers=headers, method=method)
    
    try:
        with urllib.request.urlopen(req) as response:
            return response.getcode(), json.loads(response.read().decode())
    except Exception as e:
        print(f"Request failed: {e}")
        return None, None

def setup_demo_data():
    """Create demo data for GraphQL testing"""
    print("🔄 Setting up demo data...")
    
    # Reset network
    status, _ = make_request("GET", f"{BASE_URL}/reset")
    print(f"   Reset network: {status}")
    time.sleep(1)
    
    # Create wallets
    wallets = [
        {"address": "alice-wallet-123", "balance": 1000000000000},
        {"address": "bob-wallet-456", "balance": 500000000000}
    ]
    
    for wallet in wallets:
        status, _ = make_request("POST", f"{BASE_URL}/wallet", wallet)
        print(f"   Created wallet {wallet['address']}: {status}")
    
    # Create transactions
    transactions = [
        {
            "id": "tx-alice-to-bob-001",
            "owner": "alice-public-key-123",
            "owner_address": "alice-wallet-123",
            "target": "bob-wallet-456",
            "quantity": "100000000000",  # 0.1 AR
            "reward": "1000000000",      # 0.001 AR
            "last_tx": "",
            "tags": [
                {"name": "Content-Type", "value": "text/plain"},
                {"name": "App-Name", "value": "demo-app"},
                {"name": "Action", "value": "transfer"}
            ],
            "signature": "alice-signature-001",
            "data_size": 50,
            "data_root": "alice-data-root-001"
        },
        {
            "id": "tx-bob-to-alice-002",
            "owner": "bob-public-key-456",
            "owner_address": "bob-wallet-456",
            "target": "alice-wallet-123",
            "quantity": "50000000000",   # 0.05 AR
            "reward": "1000000000",      # 0.001 AR
            "last_tx": "",
            "tags": [
                {"name": "Content-Type", "value": "application/json"},
                {"name": "App-Name", "value": "demo-app"},
                {"name": "Action", "value": "reply"}
            ],
            "signature": "bob-signature-002",
            "data_size": 100,
            "data_root": "bob-data-root-002"
        },
        {
            "id": "tx-alice-data-003",
            "owner": "alice-public-key-123",
            "owner_address": "alice-wallet-123",
            "target": "",
            "quantity": "0",
            "reward": "2000000000",      # 0.002 AR
            "last_tx": "",
            "tags": [
                {"name": "Content-Type", "value": "image/png"},
                {"name": "App-Name", "value": "photo-app"},
                {"name": "Category", "value": "profile-pic"}
            ],
            "signature": "alice-signature-003",
            "data_size": 5000,
            "data_root": "alice-data-root-003"
        }
    ]
    
    for tx in transactions:
        status, _ = make_request("POST", f"{BASE_URL}/tx", tx)
        print(f"   Created transaction {tx['id']}: {status}")
    
    # Mine blocks to confirm transactions
    status, _ = make_request("GET", f"{BASE_URL}/mine/2")
    print(f"   Mined 2 blocks: {status}")
    time.sleep(1)
    
    print("✅ Demo data setup complete!")

def test_graphql_query(title, query, variables=None):
    """Test a GraphQL query and display results"""
    print(f"\n📊 {title}")
    print("=" * 50)
    
    payload = {"query": query}
    if variables:
        payload["variables"] = variables
    
    status, response = make_request("POST", f"{BASE_URL}/graphql", payload)
    
    if status == 200:
        print("✅ Query successful!")
        if response.get('data'):
            print(json.dumps(response['data'], indent=2))
        if response.get('errors'):
            print("⚠️  Errors:", response['errors'])
    else:
        print(f"❌ Query failed with status {status}")
        if response:
            print(json.dumps(response, indent=2))

def main():
    """Run GraphQL demo"""
    print("🚀 ArLocal Python GraphQL Demo")
    print("=" * 60)
    
    # Setup demo data
    setup_demo_data()
    
    # Test 1: List all transactions
    test_graphql_query(
        "All Transactions",
        """
        {
            transactions(first: 10) {
                pageInfo {
                    hasNextPage
                }
                edges {
                    cursor
                    node {
                        id
                        owner {
                            address
                        }
                        recipient
                        quantity {
                            ar
                            winston
                        }
                        tags {
                            name
                            value
                        }
                    }
                }
            }
        }
        """
    )
    
    # Test 2: Get specific transaction
    test_graphql_query(
        "Specific Transaction",
        """
        query GetTransaction($id: String!) {
            transaction(id: $id) {
                id
                anchor
                signature
                recipient
                owner {
                    address
                    key
                }
                fee {
                    winston
                    ar
                }
                quantity {
                    winston
                    ar
                }
                data {
                    size
                    type
                }
                tags {
                    name
                    value
                }
                block {
                    id
                    height
                    timestamp
                }
            }
        }
        """,
        {"id": "tx-alice-to-bob-001"}
    )
    
    # Test 3: Filter by owner
    test_graphql_query(
        "Transactions by Owner",
        """
        query GetTransactionsByOwner($owners: [String!]) {
            transactions(owners: $owners, first: 5) {
                edges {
                    node {
                        id
                        owner {
                            address
                        }
                        recipient
                        quantity {
                            ar
                        }
                    }
                }
            }
        }
        """,
        {"owners": ["alice-wallet-123"]}
    )
    
    # Test 4: Filter by tags
    test_graphql_query(
        "Transactions by Tag",
        """
        query GetTransactionsByTag($tags: [TagFilter!]) {
            transactions(tags: $tags, first: 5) {
                edges {
                    node {
                        id
                        tags {
                            name
                            value
                        }
                    }
                }
            }
        }
        """,
        {"tags": [{"name": "App-Name", "values": ["demo-app"]}]}
    )
    
    # Test 5: Get blocks
    test_graphql_query(
        "Recent Blocks",
        """
        {
            blocks(first: 5, sort: HEIGHT_DESC) {
                pageInfo {
                    hasNextPage
                }
                edges {
                    cursor
                    node {
                        id
                        height
                        timestamp
                        previous
                    }
                }
            }
        }
        """
    )
    
    print("\n🎉 GraphQL Demo Complete!")
    print("=" * 60)
    print("The ArLocal Python GraphQL endpoint is fully functional!")
    print("You can now use it with any GraphQL client or the Arweave SDK.")

if __name__ == "__main__":
    main()
