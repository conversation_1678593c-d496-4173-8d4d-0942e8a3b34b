name: Publish to NPM
on:
  push: 
    tags:
      - v*

jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 16
      - run: npm install
      - run: npm run build
      - uses: JS-DevTools/npm-publish@v1
        with:
          token: ${{ secrets.NPM_TOKEN }}
          access: public