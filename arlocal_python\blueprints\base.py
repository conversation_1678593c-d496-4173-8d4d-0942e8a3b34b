"""
Base routes blueprint for ArLocal Python Implementation
Handles status, info, peers, logs, reset, price calculation, and mining routes
"""

from flask import Blueprint, request, jsonify, Response
import time
import random
import logging
from utils import calculate_price

# Create blueprint
base_bp = Blueprint('base', __name__)

# Get logger
logger = logging.getLogger(__name__)

# In-memory logs (shared across blueprints)
# This will be accessed via current_app.logs
logs = []

def format_balance(balance: float) -> str:
    """Format balance as integer if it's a whole number, otherwise as float"""
    if balance == int(balance):
        return str(int(balance))
    return str(balance)

# Status and Info Routes
@base_bp.route('/', methods=['GET'])
@base_bp.route('/info', methods=['GET'])
def get_status():
    """Get network status information"""
    from flask import current_app
    db = current_app.db
    network_config = current_app.network_config
    
    network_info = db.get_network_info()
    return jsonify({
        **network_config,
        **network_info
    })

@base_bp.route('/peers', methods=['GET'])
def get_peers():
    """Get peer information"""
    host = request.headers.get('Host', 'localhost:1984')
    return jsonify([host])

@base_bp.route('/logs', methods=['GET'])
def get_logs():
    """Get application logs"""
    try:
        # In the original, this reads from a logs file
        # For simplicity, we'll return our in-memory logs
        from flask import current_app
        log_content = '\n'.join(current_app.logs) if current_app.logs else 'No logs available'
        return Response(log_content, mimetype='text/plain')
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({"error": str(e)}), 500

@base_bp.route('/reset', methods=['GET'])
def reset_network():
    """Reset the network state"""
    try:
        from flask import current_app
        db = current_app.db
        network_config = current_app.network_config
        
        # Reset the network state using database
        result = db.reset_network(network_config)
        current_app.logs.append(f"Network reset at {time.time()}")
        return Response('reset done', mimetype='text/plain')
    except Exception as e:
        logger.error(f"Error resetting network: {e}")
        return jsonify({"error": str(e)}), 500

# Price Calculation Route
@base_bp.route('/price/<int:bytes_size>', methods=['GET'])
@base_bp.route('/price/<int:bytes_size>/<address>', methods=['GET'])
def get_price(bytes_size: int, address: str = None):
    """Calculate transaction price based on data size"""
    from flask import current_app
    price = calculate_price(bytes_size, current_app.config['DEFAULT_PRICE_PER_KB'])
    return Response(str(price), mimetype='text/plain')

# Mining Routes
@base_bp.route('/mine', methods=['GET'])
@base_bp.route('/mine/<int:qty>', methods=['GET'])
def mine_blocks(qty: int = 1):
    """Mine blocks"""
    try:
        from flask import current_app
        db = current_app.db
        network_config = current_app.network_config
        
        # Mine blocks using database
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            current_app.logs.append(f"Mined {qty} blocks: {new_block_ids}")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks: {e}")
        return jsonify({"error": str(e)}), 500

@base_bp.route('/mineWithFails', methods=['GET'])
@base_bp.route('/mineWithFails/<int:qty>', methods=['GET'])
def mine_blocks_with_fails(qty: int = 1):
    """Mine blocks with failure simulation"""
    try:
        from flask import current_app
        db = current_app.db
        network_config = current_app.network_config
        
        # Simulate failure rate (from config)
        fail_rate = current_app.config['DEFAULT_MINE_FAIL_RATE']

        # Get pending transactions and simulate failures
        pending_txs = db.get_pending_transactions()
        successful_txs = []

        for txid in pending_txs:
            if random.random() >= fail_rate:  # Transaction succeeds
                successful_txs.append(txid)
            else:  # Transaction fails - remove it
                db.delete_transaction(txid)

        # For simplicity, just mine normally with successful transactions
        # In a real implementation, you'd want to modify the mine_blocks method
        # to accept a specific list of transactions
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            current_app.logs.append(f"Mined {qty} blocks with {fail_rate*100}% fail rate")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks with fails: {e}")
        return jsonify({"error": str(e)}), 500

# Block Routes
@base_bp.route('/block/hash/<indep_hash>', methods=['GET'])
def get_block_by_hash(indep_hash: str):
    """Get block by independent hash"""
    from flask import current_app
    db = current_app.db
    
    block = db.get_block_by_hash(indep_hash)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })

@base_bp.route('/block/height/<int:height>', methods=['GET'])
def get_block_by_height(height: int):
    """Get block by height"""
    from flask import current_app
    db = current_app.db
    
    block = db.get_block_by_height(height)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })
