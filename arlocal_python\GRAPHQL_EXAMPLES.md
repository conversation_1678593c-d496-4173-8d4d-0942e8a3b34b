# GraphQL Examples for ArLocal Python

## Testing the GraphQL Endpoint

The GraphQL endpoint is available at: `http://localhost:1984/graphql`

### 1. Get All Transactions

```graphql
{
  transactions(first: 10) {
    pageInfo {
      hasNextPage
    }
    edges {
      cursor
      node {
        id
        owner {
          address
        }
        recipient
        quantity {
          ar
          winston
        }
        tags {
          name
          value
        }
      }
    }
  }
}
```

### 2. Get Specific Transaction

```graphql
query GetTransaction($id: String!) {
  transaction(id: $id) {
    id
    anchor
    signature
    recipient
    owner {
      address
      key
    }
    fee {
      winston
      ar
    }
    quantity {
      winston
      ar
    }
    data {
      size
      type
    }
    tags {
      name
      value
    }
    block {
      id
      height
      timestamp
    }
  }
}
```

Variables:
```json
{
  "id": "your-transaction-id-here"
}
```

### 3. Filter Transactions by Owner

```graphql
query GetTransactionsByOwner($owners: [String!]) {
  transactions(owners: $owners, first: 5) {
    edges {
      node {
        id
        owner {
          address
        }
        recipient
        quantity {
          ar
        }
      }
    }
  }
}
```

Variables:
```json
{
  "owners": ["wallet-address-here"]
}
```

### 4. Filter Transactions by Tags

```graphql
query GetTransactionsByTag($tags: [TagFilter!]) {
  transactions(tags: $tags, first: 10) {
    edges {
      node {
        id
        tags {
          name
          value
        }
      }
    }
  }
}
```

Variables:
```json
{
  "tags": [
    {
      "name": "Content-Type",
      "values": ["text/plain", "application/json"]
    }
  ]
}
```

### 5. Get Recent Blocks

```graphql
{
  blocks(first: 5, sort: HEIGHT_DESC) {
    pageInfo {
      hasNextPage
    }
    edges {
      cursor
      node {
        id
        height
        timestamp
        previous
      }
    }
  }
}
```

### 6. Get Specific Block

```graphql
query GetBlock($id: String) {
  block(id: $id) {
    id
    height
    timestamp
    previous
  }
}
```

Variables:
```json
{
  "id": "block-id-here"
}
```

### 7. Complex Query with Multiple Filters

```graphql
query ComplexQuery($owners: [String!], $tags: [TagFilter!], $block: BlockFilter) {
  transactions(
    owners: $owners
    tags: $tags
    block: $block
    first: 20
    sort: HEIGHT_DESC
  ) {
    pageInfo {
      hasNextPage
    }
    edges {
      cursor
      node {
        id
        owner {
          address
        }
        recipient
        quantity {
          ar
        }
        tags {
          name
          value
        }
        block {
          height
          timestamp
        }
      }
    }
  }
}
```

Variables:
```json
{
  "owners": ["wallet1", "wallet2"],
  "tags": [
    {
      "name": "App-Name",
      "values": ["my-app"]
    }
  ],
  "block": {
    "min": 100,
    "max": 200
  }
}
```

## Testing with curl

```bash
# Simple query
curl -X POST http://localhost:1984/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ transactions(first: 1) { edges { node { id } } } }"}'

# Query with variables
curl -X POST http://localhost:1984/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetTransaction($id: String!) { transaction(id: $id) { id owner { address } } }",
    "variables": {"id": "your-tx-id"}
  }'
```

## Testing with Python

```python
import requests
import json

# Simple query
query = """
{
  transactions(first: 5) {
    edges {
      node {
        id
        owner {
          address
        }
      }
    }
  }
}
"""

response = requests.post(
    'http://localhost:1984/graphql',
    json={'query': query},
    headers={'Content-Type': 'application/json'}
)

print(json.dumps(response.json(), indent=2))
```

## Available GraphQL Clients

You can test the endpoint with any GraphQL client:
- **GraphQL Playground** - Web-based GraphQL IDE
- **Insomnia** - REST/GraphQL client
- **Postman** - API testing tool with GraphQL support
- **GraphiQL** - In-browser GraphQL IDE

## Schema Introspection

The endpoint supports GraphQL introspection queries to explore the schema:

```graphql
{
  __schema {
    types {
      name
      description
    }
  }
}
```
