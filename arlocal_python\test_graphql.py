"""
Test script for GraphQL endpoints in ArLocal Python
"""

import requests
import json
import time

BASE_URL = "http://localhost:1984"

def test_graphql_endpoint(query, variables=None):
    """Test a GraphQL query"""
    url = f"{BASE_URL}/graphql"
    payload = {
        "query": query
    }
    if variables:
        payload["variables"] = variables
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    except Exception as e:
        print(f"Error: {e}")
        return None

def setup_test_data():
    """Create some test data for GraphQL queries"""
    print("Setting up test data...")
    
    # Create a wallet
    wallet_data = {
        "address": "test-wallet-address-123",
        "balance": 1000000000000  # 1 AR in winston
    }
    response = requests.post(f"{BASE_URL}/wallet", json=wallet_data)
    print(f"Created wallet: {response.status_code}")
    
    # Create a test transaction
    tx_data = {
        "id": "test-transaction-id-123",
        "owner": "test-owner-key",
        "owner_address": "test-wallet-address-123",
        "target": "test-target-address",
        "quantity": "100000000000",  # 0.1 AR
        "reward": "1000000000",      # 0.001 AR
        "last_tx": "",
        "tags": [
            {"name": "Content-Type", "value": "text/plain"},
            {"name": "App-Name", "value": "test-app"}
        ],
        "signature": "test-signature",
        "data_size": 100,
        "data_root": "test-data-root"
    }
    response = requests.post(f"{BASE_URL}/tx", json=tx_data)
    print(f"Created transaction: {response.status_code}")
    
    # Mine a block to confirm the transaction
    response = requests.get(f"{BASE_URL}/mine/1")
    print(f"Mined block: {response.status_code}")
    
    time.sleep(1)  # Give it a moment to process

def main():
    """Run GraphQL tests"""
    print("=" * 50)
    print("ArLocal Python GraphQL Tests")
    print("=" * 50)
    
    # Test GraphQL info endpoint
    print("\n1. Testing GraphQL Info (GET)")
    try:
        response = requests.get(f"{BASE_URL}/graphql")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Setup test data
    setup_test_data()
    
    # Test single transaction query
    print("\n2. Testing Single Transaction Query")
    query = """
    query GetTransaction($id: String!) {
        transaction(id: $id) {
            id
            anchor
            signature
            recipient
            owner {
                address
                key
            }
            fee {
                winston
                ar
            }
            quantity {
                winston
                ar
            }
            data {
                size
                type
            }
            tags {
                name
                value
            }
            block {
                id
                height
                timestamp
            }
        }
    }
    """
    variables = {"id": "test-transaction-id-123"}
    test_graphql_endpoint(query, variables)
    
    # Test transactions list query
    print("\n3. Testing Transactions List Query")
    query = """
    query GetTransactions($first: Int, $owners: [String!]) {
        transactions(first: $first, owners: $owners) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                    owner {
                        address
                    }
                    tags {
                        name
                        value
                    }
                }
            }
        }
    }
    """
    variables = {"first": 5, "owners": ["test-wallet-address-123"]}
    test_graphql_endpoint(query, variables)
    
    # Test tag filtering
    print("\n4. Testing Tag Filtering")
    query = """
    query GetTransactionsByTag($tags: [TagFilter!]) {
        transactions(tags: $tags, first: 10) {
            edges {
                node {
                    id
                    tags {
                        name
                        value
                    }
                }
            }
        }
    }
    """
    variables = {
        "tags": [
            {"name": "App-Name", "values": ["test-app"]}
        ]
    }
    test_graphql_endpoint(query, variables)
    
    # Test blocks query
    print("\n5. Testing Blocks Query")
    query = """
    query GetBlocks($first: Int) {
        blocks(first: $first) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                    height
                    timestamp
                    previous
                }
            }
        }
    }
    """
    variables = {"first": 5}
    test_graphql_endpoint(query, variables)
    
    # Test single block query
    print("\n6. Testing Single Block Query")
    # First get a block ID
    try:
        response = requests.get(f"{BASE_URL}/info")
        network_info = response.json()
        current_block_id = network_info.get('current', '')
        
        if current_block_id:
            query = """
            query GetBlock($id: String) {
                block(id: $id) {
                    id
                    height
                    timestamp
                    previous
                }
            }
            """
            variables = {"id": current_block_id}
            test_graphql_endpoint(query, variables)
        else:
            print("No current block ID found")
    except Exception as e:
        print(f"Error getting block info: {e}")
    
    print("\n" + "=" * 50)
    print("GraphQL Tests Complete!")
    print("\nTo run these tests:")
    print("1. Start the server: python app.py")
    print("2. Run this test: python test_graphql.py")

if __name__ == "__main__":
    main()
