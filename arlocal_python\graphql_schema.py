"""
GraphQL Schema and Resolvers for ArLocal Python
Implements the Arweave GraphQL API specification
"""

import graphene
from graphene import ObjectType, String, Int, List, Field, Argument, Boolean, Enum, ID
from typing import Dict, List as ListType, Optional, Any
import json
import base64
from utils import b64url_to_bytes, bytes_to_b64url
import logging

logger = logging.getLogger(__name__)

# Enums
class SortOrder(Enum):
    HEIGHT_ASC = "HEIGHT_ASC"
    HEIGHT_DESC = "HEIGHT_DESC"

class TagOperator(Enum):
    EQ = "EQ"
    NEQ = "NEQ"

# Input Types
class TagFilter(graphene.InputObjectType):
    name = String(required=True)
    values = List(String, required=True)
    op = TagOperator(default_value="EQ")

class BlockFilter(graphene.InputObjectType):
    min = Int()
    max = Int()

# Basic Types
class Tag(ObjectType):
    name = String(required=True)
    value = String(required=True)

class Amount(ObjectType):
    winston = String(required=True)
    ar = String(required=True)

class Owner(ObjectType):
    address = String(required=True)
    key = String(required=True)

class MetaData(ObjectType):
    size = String(required=True)
    type = String()

class Block(ObjectType):
    id = ID(required=True)
    timestamp = Int(required=True)
    height = Int(required=True)
    previous = ID(required=True)

class Parent(ObjectType):
    id = ID(required=True)

class Bundle(ObjectType):
    id = ID(required=True)

class Transaction(ObjectType):
    id = ID(required=True)
    anchor = String(required=True)
    signature = String(required=True)
    recipient = String(required=True)
    owner = Field(Owner, required=True)
    fee = Field(Amount, required=True)
    quantity = Field(Amount, required=True)
    data = Field(MetaData, required=True)
    tags = List(Tag, required=True)
    block = Field(Block)
    parent = Field(Parent, deprecation_reason="Use `bundledIn`")
    bundledIn = Field(Bundle)

# Connection Types for Pagination
class PageInfo(ObjectType):
    hasNextPage = Boolean(required=True)

class TransactionEdge(ObjectType):
    cursor = String(required=True)
    node = Field(Transaction, required=True)

class TransactionConnection(ObjectType):
    pageInfo = Field(PageInfo, required=True)
    edges = List(TransactionEdge, required=True)

class BlockEdge(ObjectType):
    cursor = String(required=True)
    node = Field(Block, required=True)

class BlockConnection(ObjectType):
    pageInfo = Field(PageInfo, required=True)
    edges = List(BlockEdge, required=True)

# Utility Functions
def winston_to_ar(winston_str: str) -> str:
    """Convert winston to AR (1 AR = 1e12 winston)"""
    try:
        winston = int(winston_str or "0")
        ar = winston / 1e12
        return f"{ar:.12f}".rstrip('0').rstrip('.')
    except (ValueError, TypeError):
        return "0"

def decode_tag(tag_dict: Dict[str, str]) -> Dict[str, str]:
    """Decode base64url encoded tag name and value to UTF-8"""
    try:
        name = tag_dict.get('name', '')
        value = tag_dict.get('value', '')
        
        # Try to decode from base64url first
        try:
            decoded_name = b64url_to_bytes(name).decode('utf-8')
            decoded_value = b64url_to_bytes(value).decode('utf-8')
            return {'name': decoded_name, 'value': decoded_value}
        except Exception:
            # If decoding fails, return as-is (might already be decoded)
            return {'name': name, 'value': value}
    except Exception as e:
        logger.error(f"Error decoding tag: {e}")
        return {'name': '', 'value': ''}

def format_transaction_for_graphql(tx_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format transaction data for GraphQL response"""
    try:
        # Parse tags if they're JSON string
        tags = tx_data.get('tags', [])
        if isinstance(tags, str):
            try:
                tags = json.loads(tags)
            except json.JSONDecodeError:
                tags = []
        
        # Decode tags
        decoded_tags = [decode_tag(tag) for tag in tags]
        
        # Get content type from tags
        content_type = None
        for tag in decoded_tags:
            if tag['name'].lower() == 'content-type':
                content_type = tag['value']
                break
        
        # Format the transaction
        formatted = {
            'id': tx_data.get('id', ''),
            'anchor': tx_data.get('last_tx', ''),
            'signature': tx_data.get('signature', ''),
            'recipient': tx_data.get('target', '').strip(),
            'owner': {
                'address': tx_data.get('owner_address', ''),
                'key': tx_data.get('owner', '')
            },
            'fee': {
                'winston': tx_data.get('reward', '0'),
                'ar': winston_to_ar(tx_data.get('reward', '0'))
            },
            'quantity': {
                'winston': tx_data.get('quantity', '0'),
                'ar': winston_to_ar(tx_data.get('quantity', '0'))
            },
            'data': {
                'size': str(tx_data.get('data_size', 0)),
                'type': content_type
            },
            'tags': decoded_tags,
            'block': None,
            'parent': None,
            'bundledIn': None
        }
        
        # Add block info if transaction is mined
        if tx_data.get('block') and tx_data.get('height'):
            formatted['block'] = {
                'id': tx_data['block'],
                'timestamp': tx_data.get('timestamp', 0),
                'height': tx_data.get('height', 0),
                'previous': ''  # Would need to query block table for this
            }
        
        # Add parent/bundle info if available
        if tx_data.get('parent'):
            formatted['parent'] = {'id': tx_data['parent']}
        
        if tx_data.get('bundledIn'):
            formatted['bundledIn'] = {'id': tx_data['bundledIn']}
        
        return formatted
        
    except Exception as e:
        logger.error(f"Error formatting transaction for GraphQL: {e}")
        return {}

def format_block_for_graphql(block_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format block data for GraphQL response"""
    try:
        return {
            'id': block_data.get('id', ''),
            'timestamp': block_data.get('timestamp', block_data.get('mined_at', 0) // 1000),
            'height': block_data.get('height', 0),
            'previous': block_data.get('previous_block', '')
        }
    except Exception as e:
        logger.error(f"Error formatting block for GraphQL: {e}")
        return {}

def encode_cursor(offset: int, timestamp: int = 0) -> str:
    """Encode cursor for pagination"""
    import base64
    cursor_data = f"{timestamp}:{offset}"
    return base64.b64encode(cursor_data.encode()).decode()

def decode_cursor(cursor: str) -> tuple:
    """Decode cursor for pagination"""
    try:
        import base64
        cursor_data = base64.b64decode(cursor.encode()).decode()
        parts = cursor_data.split(':')
        timestamp = int(parts[0]) if len(parts) > 0 else 0
        offset = int(parts[1]) if len(parts) > 1 else 0
        return timestamp, offset
    except Exception:
        return 0, 0

# Query Resolvers
class Query(ObjectType):
    transaction = Field(Transaction, id=ID(required=True))
    transactions = Field(
        TransactionConnection,
        ids=List(ID),
        owners=List(String),
        recipients=List(String),
        tags=List(TagFilter),
        bundledIn=List(ID),
        block=BlockFilter(),
        first=Int(default_value=10),
        after=String(),
        sort=SortOrder(default_value="HEIGHT_DESC"),
        required=True
    )
    block = Field(Block, id=String())
    blocks = Field(
        BlockConnection,
        ids=List(ID),
        height=BlockFilter(),
        first=Int(default_value=10),
        after=String(),
        sort=SortOrder(default_value="HEIGHT_DESC"),
        required=True
    )

    def resolve_transaction(self, info, id):
        """Resolve single transaction by ID"""
        try:
            db = info.context.get('db')
            if not db:
                return None

            tx_data = db.get_transaction(id)
            if not tx_data:
                return None

            formatted = format_transaction_for_graphql(tx_data)
            return Transaction(**formatted) if formatted else None

        except Exception as e:
            logger.error(f"Error resolving transaction {id}: {e}")
            return None

    def resolve_transactions(self, info, **args):
        """Resolve paginated transactions with filtering"""
        try:
            db = info.context.get('db')
            if not db:
                return TransactionConnection(pageInfo=PageInfo(hasNextPage=False), edges=[])

            # Parse pagination
            first = min(args.get('first', 10), 100)  # Max 100 items per page
            after = args.get('after')
            timestamp, offset = decode_cursor(after) if after else (0, 0)

            # Build query parameters
            query_params = {
                'limit': first + 1,  # Get one extra to check if there's a next page
                'offset': offset,
                'ids': args.get('ids'),
                'owners': args.get('owners'),
                'recipients': args.get('recipients'),
                'tags': args.get('tags'),
                'bundledIn': args.get('bundledIn'),
                'block': args.get('block'),
                'sort': args.get('sort', 'HEIGHT_DESC')
            }

            # Get transactions from database
            transactions = db.query_transactions_for_graphql(query_params)

            # Check if there's a next page
            has_next_page = len(transactions) > first
            if has_next_page:
                transactions = transactions[:first]

            # Create edges
            edges = []
            for i, tx_data in enumerate(transactions):
                formatted = format_transaction_for_graphql(tx_data)
                if formatted:
                    cursor = encode_cursor(offset + i + 1, timestamp)
                    edges.append(TransactionEdge(
                        cursor=cursor,
                        node=Transaction(**formatted)
                    ))

            return TransactionConnection(
                pageInfo=PageInfo(hasNextPage=has_next_page),
                edges=edges
            )

        except Exception as e:
            logger.error(f"Error resolving transactions: {e}")
            return TransactionConnection(pageInfo=PageInfo(hasNextPage=False), edges=[])

    def resolve_block(self, info, id=None):
        """Resolve single block by ID"""
        try:
            if not id:
                return None

            db = info.context.get('db')
            if not db:
                return None

            block_data = db.get_block_by_hash(id)
            if not block_data:
                return None

            formatted = format_block_for_graphql(block_data)
            return Block(**formatted) if formatted else None

        except Exception as e:
            logger.error(f"Error resolving block {id}: {e}")
            return None

    def resolve_blocks(self, info, **args):
        """Resolve paginated blocks with filtering"""
        try:
            db = info.context.get('db')
            if not db:
                return BlockConnection(pageInfo=PageInfo(hasNextPage=False), edges=[])

            # Parse pagination
            first = min(args.get('first', 10), 100)  # Max 100 items per page
            after = args.get('after')
            timestamp, offset = decode_cursor(after) if after else (0, 0)

            # Build query parameters
            query_params = {
                'limit': first + 1,  # Get one extra to check if there's a next page
                'offset': offset,
                'ids': args.get('ids'),
                'height': args.get('height'),
                'sort': args.get('sort', 'HEIGHT_DESC')
            }

            # Get blocks from database
            blocks = db.query_blocks_for_graphql(query_params)

            # Check if there's a next page
            has_next_page = len(blocks) > first
            if has_next_page:
                blocks = blocks[:first]

            # Create edges
            edges = []
            for i, block_data in enumerate(blocks):
                formatted = format_block_for_graphql(block_data)
                if formatted:
                    cursor = encode_cursor(offset + i + 1, timestamp)
                    edges.append(BlockEdge(
                        cursor=cursor,
                        node=Block(**formatted)
                    ))

            return BlockConnection(
                pageInfo=PageInfo(hasNextPage=has_next_page),
                edges=edges
            )

        except Exception as e:
            logger.error(f"Error resolving blocks: {e}")
            return BlockConnection(pageInfo=PageInfo(hasNextPage=False), edges=[])

# Create the schema
schema = graphene.Schema(query=Query)
