#!/usr/bin/env python3
"""
Test script to verify wallet balance endpoint behavior
"""

import requests
import json
import sys

def test_wallet_balance_endpoint():
    """Test the wallet balance endpoint with a non-existent wallet"""
    base_url = "http://localhost:1984"
    
    # Test with a valid but non-existent wallet address
    test_address = "pLPrkgQBFCpaJm1DIksYSPaeMNy6evqoLPCPAlZ-YG8"
    
    print(f"Testing wallet balance endpoint with address: {test_address}")
    print("=" * 60)
    
    try:
        # Test GET /wallet/<address>/balance
        response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Text: '{response.text}'")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            if response.text == "0":
                print("✅ SUCCESS: Non-existent wallet returns balance '0' as expected")
                return True
            else:
                print(f"❌ FAIL: Expected '0', got '{response.text}'")
                return False
        else:
            print(f"❌ FAIL: Expected status 200, got {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ FAIL: Could not connect to ArLocal server at http://localhost:1984")
        print("Make sure the server is running with: python arlocal_python/app.py")
        return False
    except Exception as e:
        print(f"❌ FAIL: Unexpected error: {e}")
        return False

def test_wallet_mint_and_balance():
    """Test minting tokens to a wallet and checking balance"""
    base_url = "http://localhost:1984"
    test_address = "pLPrkgQBFCpaJm1DIksYSPaeMNy6evqoLPCPAlZ-YG8"
    
    print(f"\nTesting mint and balance with address: {test_address}")
    print("=" * 60)
    
    try:
        # First check initial balance (should be 0)
        response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        print(f"Initial balance: {response.text} (status: {response.status_code})")
        
        # Mint 100 tokens
        mint_response = requests.get(f"{base_url}/mint/{test_address}/100")
        print(f"Mint response: {mint_response.text} (status: {mint_response.status_code})")
        
        # Check balance again
        balance_response = requests.get(f"{base_url}/wallet/{test_address}/balance")
        print(f"New balance: {balance_response.text} (status: {balance_response.status_code})")
        
        if balance_response.text == "100":
            print("✅ SUCCESS: Mint and balance check working correctly")
            return True
        else:
            print(f"❌ FAIL: Expected balance '100', got '{balance_response.text}'")
            return False
            
    except Exception as e:
        print(f"❌ FAIL: Error during mint test: {e}")
        return False

if __name__ == "__main__":
    print("ArLocal Python Wallet Balance Test")
    print("=" * 60)
    
    # Test 1: Non-existent wallet should return '0'
    test1_passed = test_wallet_balance_endpoint()
    
    # Test 2: Mint and balance check
    test2_passed = test_wallet_mint_and_balance()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"Test 1 (Non-existent wallet): {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Test 2 (Mint and balance): {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("🎉 All tests PASSED!")
        sys.exit(0)
    else:
        print("❌ Some tests FAILED!")
        sys.exit(1)
