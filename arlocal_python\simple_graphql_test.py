"""
Simple GraphQL test using only built-in libraries
"""

import urllib.request
import urllib.parse
import json

def test_graphql_query():
    """Test a simple GraphQL query"""
    
    # Test GET request first
    print("Testing GraphQL GET endpoint...")
    try:
        with urllib.request.urlopen('http://localhost:1984/graphql') as response:
            data = json.loads(response.read().decode())
            print("GET Response:", json.dumps(data, indent=2))
    except Exception as e:
        print(f"GET Error: {e}")
        return
    
    # Test a simple GraphQL query
    print("\nTesting GraphQL POST query...")
    
    query = """
    query {
        transactions(first: 1) {
            pageInfo {
                hasNextPage
            }
            edges {
                cursor
                node {
                    id
                }
            }
        }
    }
    """
    
    payload = {
        "query": query
    }
    
    try:
        data = json.dumps(payload).encode('utf-8')
        req = urllib.request.Request(
            'http://localhost:1984/graphql',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
            print("GraphQL Response:", json.dumps(result, indent=2))
            
    except Exception as e:
        print(f"GraphQL Error: {e}")

if __name__ == "__main__":
    test_graphql_query()
