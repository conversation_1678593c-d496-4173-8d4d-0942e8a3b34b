"""
Simple test to verify GraphQL is working
"""

import urllib.request
import json

def test_graphql():
    # Test GET first
    print("Testing GraphQL GET...")
    try:
        with urllib.request.urlopen('http://localhost:1984/graphql') as response:
            data = json.loads(response.read().decode())
            print("✅ GET works:", data.get('message', 'No message'))
    except Exception as e:
        print("❌ GET failed:", e)
        return False
    
    # Test simple POST query
    print("\nTesting GraphQL POST...")
    query = '{ transactions(first: 1) { edges { node { id } } } }'
    payload = json.dumps({"query": query}).encode('utf-8')
    
    try:
        req = urllib.request.Request(
            'http://localhost:1984/graphql',
            data=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode())
            print("✅ POST works:", json.dumps(result, indent=2))
            return True
    except Exception as e:
        print("❌ POST failed:", e)
        return False

if __name__ == "__main__":
    test_graphql()
