#!/usr/bin/env python3
"""
Test script for the ArLocal database functionality
"""

import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import ArLocalDatabase
from utils import random_id, validate_txid, calculate_price

def test_database():
    """Test basic database functionality"""
    print("Testing ArLocal Database...")
    
    # Initialize database
    db = ArLocalDatabase(':memory:', ':memory:')
    
    # Test network info
    print("\n1. Testing network info...")
    network_info = db.get_network_info()
    print(f"Network info: {network_info}")
    
    # Test wallet creation
    print("\n2. Testing wallet operations...")
    address = random_id(43)
    print(f"Generated address: {address}")
    
    # Create wallet
    success = db.create_wallet(address, 1000.0)
    print(f"Wallet created: {success}")
    
    # Get wallet balance
    balance = db.get_wallet_balance(address)
    print(f"Wallet balance: {balance}")
    
    # Test transaction creation
    print("\n3. Testing transaction operations...")
    txid = random_id(43)
    print(f"Generated txid: {txid}")
    
    tx_data = {
        'id': txid,
        'owner': address,
        'target': '',
        'quantity': '0',
        'reward': '100',
        'signature': 'test_signature',
        'last_tx': '',
        'data_size': 1024,
        'tags': [{'name': 'Content-Type', 'value': 'text/plain'}],
        'format': 2
    }
    
    # Insert transaction
    success = db.insert_transaction(tx_data)
    print(f"Transaction inserted: {success}")
    
    # Get transaction
    transaction = db.get_transaction(txid)
    print(f"Retrieved transaction: {transaction is not None}")
    if transaction:
        print(f"Transaction ID: {transaction['id']}")
        print(f"Transaction tags: {transaction['tags']}")
    
    # Test pending transactions
    pending = db.get_pending_transactions()
    print(f"Pending transactions: {pending}")
    
    # Test block mining
    print("\n4. Testing block mining...")
    success, block_ids = db.mine_blocks(1)
    print(f"Mining success: {success}")
    print(f"New block IDs: {block_ids}")
    
    # Check network info after mining
    network_info = db.get_network_info()
    print(f"Network info after mining: {network_info}")
    
    # Test chunk operations
    print("\n5. Testing chunk operations...")
    chunk_data = {
        'chunk': 'test_chunk_data_base64',
        'data_root': 'test_root',
        'data_size': 1024,
        'offset': 1000,
        'data_path': 'test/path'
    }
    
    success = db.insert_chunk(chunk_data)
    print(f"Chunk inserted: {success}")
    
    chunk = db.get_chunk_by_offset(1000)
    print(f"Retrieved chunk: {chunk is not None}")
    
    # Test utility functions
    print("\n6. Testing utility functions...")
    print(f"Valid txid: {validate_txid(txid)}")
    print(f"Invalid txid: {validate_txid('invalid')}")
    
    price = calculate_price(1024, 65595508)
    print(f"Price for 1024 bytes: {price}")
    
    print("\nAll tests completed!")

if __name__ == '__main__':
    test_database()
