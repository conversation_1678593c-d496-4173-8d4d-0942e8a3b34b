{"name": "arlocal", "version": "1.1.66", "main": "./bin/app.js", "repository": "https://github.com/textury/arlocal.git", "author": "Cedrik <<EMAIL>>", "license": "MIT", "files": ["bin/**/*"], "bin": {"arlocal": "./bin/index.js"}, "engines": {"node": ">=15.14.0"}, "scripts": {"start": "node ./bin", "build": "rimraf bin && tsc && copyfiles -u 1 \"./src/**/*.graphql\" bin", "dev": "tsc-watch --onSuccess \"node bin\"", "cmd": "yarn build && yarn start", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --silent", "format": "prettier --write \"src/**/*.ts\"", "lint": "tslint -p tsconfig.json", "precommit": "npm run format && npm run lint && npm run build", "prepare": "npm run build", "prepublishOnly": "npm run lint", "preversion": "npm run lint", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags"}, "dependencies": {"@koa/cors": "^3.1.0", "apollo-server-koa": "^3.6.2", "arbundles": "^0.6.19", "arweave-bundles": "^1.0.3", "asn1.js": "5.4.1", "bignumber.js": "^9.0.2", "bytes": "^3.1.2", "graphql": "^16.2.0", "knex": "^0.95.6", "koa": "^2.13.4", "koa-body": "^4.2.0", "koa-bodyparser": "^4.3.0", "koa-json": "^2.0.2", "koa-logger": "^3.2.1", "koa-router": "^10.1.1", "lodash": "^4.17.21", "mime": "^3.0.0", "minimist": "^1.2.5", "moment": "^2.29.1", "rfc4648": "^1.5.1", "smartweave": "^0.4.46", "sqlite3": "^5.0.3", "tsc-watch": "^4.6.0"}, "devDependencies": {"@assemblyscript/loader": "^0.19.23", "@types/bytes": "^3.1.1", "@types/jest": "^27.4.0", "@types/koa": "^2.13.4", "@types/koa-bodyparser": "^4.3.5", "@types/koa-json": "^2.0.20", "@types/koa-logger": "^3.1.2", "@types/koa-router": "^7.4.4", "@types/lodash": "^4.14.178", "@types/mime": "^2.0.3", "@types/minimist": "^1.2.2", "@types/nedb": "^1.8.12", "@types/node": "^17.0.10", "@types/supertest": "^2.0.11", "ardb": "^1.1.8", "arkb": "^1.1.48", "arweave": "^1.10.23", "blockweave": "^1.0.17", "copyfiles": "^2.4.1", "jest": "^27.4.7", "prettier": "^2.5.1", "redstone-smartweave": "^0.4.47", "redstone-wasm-metering": "^1.0.1", "rimraf": "^3.0.2", "supertest": "^6.2.2", "ts-jest": "^27.1.3", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.5.5"}}